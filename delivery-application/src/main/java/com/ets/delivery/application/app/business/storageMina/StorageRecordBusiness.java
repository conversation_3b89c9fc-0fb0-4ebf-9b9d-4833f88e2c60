package com.ets.delivery.application.app.business.storageMina;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.business.BaseBusiness;
import com.ets.delivery.application.app.business.CosBusiness;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.app.business.serial.SerialBusiness;
import com.ets.delivery.application.common.consts.serial.SerialTypeEnum;
import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.app.thirdservice.feign.PhpBaseFeign;
import com.ets.delivery.application.common.bo.StorageRecordLogBO;
import com.ets.delivery.application.common.config.GoodsConfig;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.storageRecord.GoodsTypeEnum;
import com.ets.delivery.application.common.consts.storageRecord.StorageRecordLogTypeEnum;
import com.ets.delivery.application.common.consts.storageRecord.StorageRecordStatusEnum;
import com.ets.delivery.application.common.dto.storage.*;
import com.ets.delivery.application.common.utils.UrlUtil;
import com.ets.delivery.application.common.vo.SelectOptionsVO;
import com.ets.delivery.application.common.vo.storage.*;
import com.ets.delivery.application.infra.entity.LogisticsSendBack;
import com.ets.delivery.application.infra.entity.Storage;
import com.ets.delivery.application.infra.entity.StorageRecord;
import com.ets.delivery.application.infra.entity.User;
import com.ets.delivery.application.infra.service.StorageRecordLogService;
import com.ets.delivery.application.infra.service.StorageRecordService;
import com.ets.goods.feign.feign.GoodsSkuFeign;
import com.ets.goods.feign.response.StorageGoodsSkuInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StorageRecordBusiness {

    @Autowired
    private StorageRecordService storageRecordService;

    @Autowired
    private StorageRecordLogService storageRecordLogService;

    @Autowired
    private BaseBusiness baseBusiness;

    @Autowired
    private PhpBaseFeign phpBaseFeign;

    @Autowired
    private SendBackBusiness sendBackBusiness;

    @Autowired
    private StorageBusiness storageBusiness;

    @Autowired
    private CosBusiness cosBusiness;

    @Autowired
    private GoodsConfig goodsConfig;

    @Autowired
    private GoodsSkuFeign goodsSkuFeign;

    @Autowired
    private SerialBusiness serialBusiness;

    public StorageCheckResultVO create(StorageRecordCreateDTO dto, User operateUser) {

        dto.setExpressNumber(dto.getExpressNumber().trim());
        StorageRecord exist = storageRecordService.getByExpressNumber(dto.getExpressNumber());
        if (exist != null) {
            ToolsHelper.throwException("快递单号已存在扫描记录，签收货物为：" + GoodsTypeEnum.getDescByCode(exist.getGoodsType()));
        }

        if (StringUtils.isEmpty(dto.getReviceRemark())) {
            dto.setReviceRemark(GoodsTypeEnum.getDescByCode(dto.getGoodsType()));
        }

        dto.setGoodsImages(UrlUtil.getOriginUrl(dto.getGoodsImages()));

        if (dto.getGoodsType().equals(GoodsTypeEnum.GOODS.getValue())) {
            if (dto.getSkuInfo() == null) {
                ToolsHelper.throwException("请选择商品sku");
            }
        }

        StorageRecord record = new StorageRecord();
        record.setRecordSn(baseBusiness.generateSn("StorageRecordSn"));
        record.setStorageCode(dto.getStorageCode());
        record.setExpressNumber(dto.getExpressNumber());
        record.setGoodsType(dto.getGoodsType());
        record.setGoodsImages(dto.getGoodsImages());
        record.setNums(1);
        if (dto.getSerialList() != null && !dto.getSerialList().isEmpty()) {
            // 对序列号列表进行去重处理
            List<String> distinctSerialList = dto.getSerialList().stream()
                    .distinct()
                    .collect(Collectors.toList());
            dto.setSerialList(distinctSerialList);
            record.setNums(distinctSerialList.size());
        }
        record.setReviceTime(LocalDateTime.now());
        record.setOperator(operateUser.getUsername());
        record.setReviceRemark(dto.getReviceRemark());
        record.setSkuInfo(JSON.toJSONString(dto.getSkuInfo()));

        storageRecordService.create(record);

        // 处理序列号寄回状态更新
        if (dto.getSerialList() != null && !dto.getSerialList().isEmpty()) {
            try {
                SerialProcessBO processBO = SerialProcessBO.builder()
                    .serialList(dto.getSerialList())
                    .businessSn(record.getRecordSn())
                    .storageSku("")
                    .inventoryType("")
                    .operator(operateUser.getUsername())
                    .storageCode(dto.getStorageCode())
                    .businessSource("寄回")
                    .build();
                serialBusiness.processSerials(SerialTypeEnum.RETURN, processBO);
            } catch (Exception e) {
                log.error("处理寄回序列号失败，记录单号：{}，序列号：{}，错误：{}",
                    record.getRecordSn(),
                    dto.getSerialList(),
                    e.getMessage());
            }
        }

        // 记录日志
        String content = "小程序新增入库记录：" + dto;
        StorageRecordLogBO logBO = new StorageRecordLogBO();
        logBO.setRecordId(record.getId())
                .setOperator(operateUser.getUsername())
                .setType(StorageRecordLogTypeEnum.ADD.getValue())
                .setOperateContent(content.length() > 255 ? content.substring(0, 255) : content);
        storageRecordLogService.addLog(logBO);

        return sendBackBindStorage(record, false);
    }

    public void create(StorageRecordAdminCreateDTO createDTO) {
        createDTO.setExpressNumber(createDTO.getExpressNumber().trim().toUpperCase());
        StorageRecord exist = storageRecordService.getByExpressNumber(createDTO.getExpressNumber());
        if (ObjectUtils.isNotEmpty(exist)) {
            ToolsHelper.throwException("快递单号已存在扫描记录，签收货物为：" + GoodsTypeEnum.map.getOrDefault(exist.getGoodsType(), "未知"));
        }
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);

        if (createDTO.getGoodsType().equals(GoodsTypeEnum.GOODS.getValue())) {
            if (createDTO.getSkuInfo() == null) {
                ToolsHelper.throwException("请选择商品sku");
            }
        }

        StorageRecord record = new StorageRecord();
        record.setRecordSn(baseBusiness.generateSn("StorageRecordSn"));
        record.setStorageCode(createDTO.getStorageCode());
        record.setExpressNumber(createDTO.getExpressNumber());
        record.setGoodsType(createDTO.getGoodsType());
        record.setGoodsImages(UrlUtil.getOriginUrl(createDTO.getGoodsImages()));
        record.setNums(createDTO.getNums());
        record.setReviceTime(LocalDateTime.now());
        record.setOperator(user.getUsername());
        record.setReviceRemark(GoodsTypeEnum.map.getOrDefault(createDTO.getGoodsType(), "未知"));
        record.setSkuInfo(JSON.toJSONString(createDTO.getSkuInfo()));

        storageRecordService.create(record);

        // 处理序列号寄回状态更新
        if (createDTO.getSerialList() != null && !createDTO.getSerialList().isEmpty()) {
            // 对序列号列表进行去重处理
            List<String> distinctSerialList = createDTO.getSerialList().stream()
                    .distinct()
                    .collect(Collectors.toList());
            createDTO.setSerialList(distinctSerialList);
            try {
                SerialProcessBO processBO = SerialProcessBO.builder()
                        .serialList(createDTO.getSerialList())
                        .businessSn(record.getRecordSn())
                        .storageSku("")
                        .inventoryType("")
                        .operator(user.getUsername())
                        .storageCode(createDTO.getStorageCode())
                        .businessSource("寄回")
                        .build();
                serialBusiness.processSerials(SerialTypeEnum.RETURN, processBO);
            } catch (Exception e) {
                log.error("处理寄回序列号失败，记录单号：{}，序列号：{}，错误：{}",
                        record.getRecordSn(),
                        createDTO.getSerialList(),
                        e.getMessage());
            }
        }

        // 记录日志
        String content = "新增入库记录：" + createDTO;
        StorageRecordLogBO logBO = new StorageRecordLogBO();
        logBO.setRecordId(record.getId())
                .setOperator(user.getUsername())
                .setType(StorageRecordLogTypeEnum.ADD.getValue())
                .setOperateContent(content.length() > 255 ? content.substring(0, 255) : content);
        storageRecordLogService.addLog(logBO);

        sendBackBindStorage(record, createDTO.getDirectlySuccess());
    }

    public StorageCheckResultVO edit(StorageRecordEditDTO dto, User operateUser) {

        StorageRecord storageRecord = getByRecordSn(dto.getRecordSn());

        dto.setExpressNumber(dto.getExpressNumber().trim());
        if (! dto.getExpressNumber().equals(storageRecord.getExpressNumber())) {
            StorageRecord exist = storageRecordService.getByExpressNumber(dto.getExpressNumber());
            if (exist != null) {
                ToolsHelper.throwException("快递单号已存在扫描记录，签收货物为：" + GoodsTypeEnum.getDescByCode(exist.getGoodsType()));
            }
        }

        dto.setGoodsImages(UrlUtil.getOriginUrl(dto.getGoodsImages()));

        if (dto.getGoodsType().equals(GoodsTypeEnum.GOODS.getValue())) {
            if (dto.getSkuInfo() == null) {
                ToolsHelper.throwException("请选择商品sku");
            }
        }

        if (StringUtils.isEmpty(dto.getReviceRemark())) {
            dto.setReviceRemark(GoodsTypeEnum.getDescByCode(dto.getGoodsType()));
        }
        storageRecordService.edit(dto, storageRecord);

        // 检测序列号变更
        SerialBusiness.SerialChangeResult changeResult = serialBusiness.detectSerialChange(
            storageRecord.getRecordSn(), dto.getSerialList());

        // 更新DTO中的序列号列表（已去重）
        dto.setSerialList(changeResult.getNewSerialList());

        // 如果序列号有变动，处理序列号记录更新
        if (changeResult.isChanged()) {
            try {
                serialBusiness.processStorageRecordSerialUpdate(storageRecord.getRecordSn(),
                    changeResult.getOriginalSerialList(), changeResult.getNewSerialList(),
                    operateUser.getUsername(), dto.getStorageCode(), "寄回");
            } catch (Exception e) {
                log.error("处理序列号记录更新失败，记录单号：{}，错误：{}",
                    storageRecord.getRecordSn(), e.getMessage());
            }
        }

        // 记录日志
        String content = "小程序编辑入库记录：" + dto;
        StorageRecordLogBO logBO = new StorageRecordLogBO();
        logBO.setRecordId(storageRecord.getId())
                .setOperator(operateUser.getUsername())
                .setType(StorageRecordLogTypeEnum.EDIT.getValue())
                .setOperateContent(content.length() > 255 ? content.substring(0, 255) : content);
        storageRecordLogService.addLog(logBO);

        return sendBackBindStorage(storageRecord, false);
    }

    public void edit(StorageRecordAdminEditDTO editDTO) {
        StorageRecord storageRecord = storageRecordService.getById(editDTO.getId());
        if (ObjectUtils.isEmpty(storageRecord)) {
            ToolsHelper.throwException("入库记录不存在");
        }

        if (storageRecord.getStatus().equals(StorageRecordStatusEnum.SUCCESS.getValue())) {
            ToolsHelper.throwException("已匹配成功的记录不能修改");
        }

        editDTO.setExpressNumber(editDTO.getExpressNumber().trim().toUpperCase());
        editDTO.setGoodsImages(UrlUtil.getOriginUrl(editDTO.getGoodsImages()));
        if (!editDTO.getExpressNumber().equals(storageRecord.getExpressNumber())) {
            StorageRecord exist = storageRecordService.getByExpressNumber(editDTO.getExpressNumber());
            if (ObjectUtils.isNotEmpty(exist)) {
                ToolsHelper.throwException("快递单号已存在扫描记录，签收货物为：" + GoodsTypeEnum.map.getOrDefault(exist.getGoodsType(), "未知"));
            }
        }

        // 检测序列号变更
        SerialBusiness.SerialChangeResult changeResult = serialBusiness.detectSerialChange(
            storageRecord.getRecordSn(), editDTO.getSerialList());

        // 更新DTO中的序列号列表（已去重）
        editDTO.setSerialList(changeResult.getNewSerialList());

        // 有变动才修改
        if (!storageRecord.getStorageCode().equals(editDTO.getStorageCode()) ||
                !storageRecord.getExpressNumber().equals(editDTO.getExpressNumber()) ||
                !storageRecord.getGoodsType().equals(editDTO.getGoodsType()) ||
                !storageRecord.getGoodsImages().equals(editDTO.getGoodsImages()) ||
                !storageRecord.getNums().equals(editDTO.getNums()) ||
                editDTO.getDirectlySuccess() ||
                changeResult.isChanged()) {
            StorageRecord record = BeanUtil.copyProperties(editDTO, StorageRecord.class);
            record.setUpdatedAt(LocalDateTime.now());
            storageRecordService.updateById(record);
            storageRecord.setExpressNumber(record.getExpressNumber());
            storageRecord.setGoodsType(record.getGoodsType());
            storageRecord.setGoodsImages(record.getGoodsImages());
            storageRecord.setNums(record.getNums());

            User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);

            // 如果序列号有变动，处理序列号记录更新
            if (changeResult.isChanged()) {
                try {
                    serialBusiness.processStorageRecordSerialUpdate(storageRecord.getRecordSn(),
                        changeResult.getOriginalSerialList(), changeResult.getNewSerialList(),
                        user.getUsername(), editDTO.getStorageCode(), "寄回");
                } catch (Exception e) {
                    log.error("处理序列号记录更新失败，记录单号：{}，错误：{}",
                        storageRecord.getRecordSn(), e.getMessage());
                }
            }

            // 记录日志
            String content = "编辑入库记录：" + editDTO;
            StorageRecordLogBO logBO = new StorageRecordLogBO();
            logBO.setRecordId(record.getId())
                    .setOperator(user.getUsername())
                    .setType(StorageRecordLogTypeEnum.EDIT.getValue())
                    .setOperateContent(content.length() > 255 ? content.substring(0, 255) : content);
            storageRecordLogService.addLog(logBO);

            sendBackBindStorage(storageRecord, editDTO.getDirectlySuccess());
        }
    }

    public StorageCheckResultVO sendBackBindStorage(StorageRecord storageRecord, Boolean directlySuccess) {

        StorageCheckResultVO vo = new StorageCheckResultVO();
        vo.setRecordSn(storageRecord.getRecordSn());
        try {
            LogisticsSendBack sendBack = sendBackBusiness.getByExpressNumber(storageRecord.getExpressNumber());

            vo = sendBackBusiness.checkAndUpdate(sendBack, storageRecord, directlySuccess);
        } catch (Exception e) {

            vo.setIsSuccess(false);
            vo.setRemark(e.getMessage());

            storageRecordService.updateReceiveInfo(storageRecord.getRecordSn(), false, "异常：" + e.getMessage());
        }

        return vo;
    }

    public StorageRecord getByRecordSn(String recordSn) {

        StorageRecord storageRecord = storageRecordService.getByRecordSn(recordSn);

        if (storageRecord == null) {
            ToolsHelper.throwException("入库记录不存在：" + recordSn);
        }

        return storageRecord;
    }

    public StorageRecordGetDataVO getData(String recordSn) {

        StorageRecord storageRecord = getByRecordSn(recordSn);

        StorageRecordGetDataVO vo = BeanUtil.copyProperties(storageRecord, StorageRecordGetDataVO.class);

        // 获取序列号列表
        try {
            List<String> serialList = serialBusiness.getSerialListByRecordSn(recordSn);
            vo.setSerialList(serialList);
        } catch (Exception e) {
            log.warn("获取序列号列表失败，记录单号：{}，错误：{}", recordSn, e.getMessage());
            vo.setSerialList(new ArrayList<>());
        }

        return vo;
    }

    public JSONObject getUploadInfo() {
        // 调gd-micro-base接口
        String json = phpBaseFeign.getUploadInfo("storage");
        JsonResult<JSONObject> result = JsonResult.convertFromJsonStr(json, JSONObject.class);
        result.checkError();

        return result.getData();
    }

    public HashMap<String, List<?>> getOptions() {

        List<SelectOptionsVO> list = new ArrayList<>();
        for(GoodsTypeEnum node: GoodsTypeEnum.values()) {
            SelectOptionsVO vo = new SelectOptionsVO(String.valueOf(node.getValue()), node.getDesc());
            list.add(vo);
        }
        HashMap<String, List<?>> map = new HashMap<>();
        map.put("goodsType", list);

        List<SelectOptionsVO> statusList = new ArrayList<>();
        for(StorageRecordStatusEnum statusEnum: StorageRecordStatusEnum.values()) {
            SelectOptionsVO statusVo = new SelectOptionsVO(String.valueOf(statusEnum.getValue()), statusEnum.getDesc());
            statusList.add(statusVo);
        }
        map.put("status", statusList);

        // 可选的商品信息
        if (goodsConfig.getGoodsCodeList() != null) {
            List<StorageGoodsSkuInfoVO> goodsInfo = goodsSkuFeign.getStorageGoodsSkuList(goodsConfig.getGoodsCodeList()).getData();
            map.put("goodsInfo", goodsInfo);
        } else {
            map.put("goodsInfo", null);
        }

        return map;
    }

    public IPage<StorageRecordVO> getHistoryList(StorageHistoryListDTO dto, User user) {

        // 分页设置
        IPage<StorageRecord> oPage = new Page<>(dto.getPage(), dto.getSize(), true);

        // 查询条件设置
        LambdaQueryWrapper<StorageRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(StringUtils.isNotEmpty(dto.getExpressNumber()), StorageRecord::getExpressNumber, dto.getExpressNumber())
                .eq(dto.getStatus() != null, StorageRecord::getStatus, dto.getStatus())
                .in(StorageRecord::getStorageCode, Arrays.asList(user.getStorageCode().split(",")))
                .orderByDesc(StorageRecord::getCreatedAt);

        IPage<StorageRecord> pageList = storageRecordService.getPageListByWrapper(oPage, wrapper);
        if (pageList.getSize() == 0) {
            return null;
        }

        Map<String, Storage> storageMap = storageBusiness.getListMap();

        return pageList.convert(entity -> {

            StorageRecordVO vo = BeanUtil.copyProperties(entity, StorageRecordVO.class);

            vo.setStatusStr(StorageRecordStatusEnum.map.getOrDefault(entity.getStatus(), ""));
            vo.setGoodsTypeStr(GoodsTypeEnum.getDescByCode(entity.getGoodsType()));
            vo.setSkuInfoObj(entity.getSkuInfoObj());

            if (storageMap.containsKey(entity.getStorageCode())) {
                vo.setStorageName(storageMap.get(entity.getStorageCode()).getName());
                vo.setStorageImage(storageMap.get(entity.getStorageCode()).getStorageImages());
            }

            return vo;
        });
    }

    public IPage<StorageRecordAdminListVO> getList(StorageRecordAdminListDTO listDTO) {
        IPage<StorageRecord> page = storageRecordService.getPage(listDTO);
        return page.convert(storageRecord -> BeanUtil.copyProperties(storageRecord, StorageRecordAdminListVO.class));
    }

    public StorageRecordDetailVO getDetail(StorageRecordDetailDTO detailDTO) {
        StorageRecord storageRecord = storageRecordService.getById(detailDTO.getRecordId());
        if (ObjectUtils.isEmpty(storageRecord)) {
            ToolsHelper.throwException("入库记录不存在");
        }

        StorageRecordDetailVO vo = BeanUtil.copyProperties(storageRecord, StorageRecordDetailVO.class);

        // 获取序列号列表
        try {
            List<String> serialList = serialBusiness.getSerialListByRecordSn(storageRecord.getRecordSn());
            vo.setSerialList(serialList);
        } catch (Exception e) {
            log.warn("获取序列号列表失败，记录单号：{}，错误：{}", storageRecord.getRecordSn(), e.getMessage());
            vo.setSerialList(new ArrayList<>());
        }

        return vo;
    }

    public IPage<StorageRecordAdminLogVO> getLog(StorageRecordAdminLogDTO logDTO) {
        return storageRecordLogService.getLogListById(logDTO.getRecordId(), logDTO.getPageNum(), logDTO.getPageSize())
                .convert(storageRecordLog -> BeanUtil.copyProperties(storageRecordLog, StorageRecordAdminLogVO.class));
    }

    public StorageRecordUploadImgVO uploadImg(MultipartFile file) {
        StorageRecordUploadImgVO vo = new StorageRecordUploadImgVO();
        try {
            // 文件名
            String filename = "/storage/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "/" + SecureUtil.md5(UUID.randomUUID().toString());

            // 上传cos
            String url = cosBusiness.upload(file.getBytes(), filename);
            vo.setUrl(url);
        } catch (IOException e) {
            ToolsHelper.throwException("上传失败：" + e.getMessage());
        }
        return vo;
    }
}
