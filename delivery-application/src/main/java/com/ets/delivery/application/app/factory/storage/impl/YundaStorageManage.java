package com.ets.delivery.application.app.factory.storage.impl;

import cn.hutool.core.convert.ConverterRegistry;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.business.WorkWeChatBusiness;
import com.ets.delivery.application.app.thirdservice.business.YundaBusiness;
import com.ets.delivery.application.app.thirdservice.request.yunda.*;
import com.ets.delivery.application.app.thirdservice.response.yunda.YundaDeliveryOrderCreateXmlVO;
import com.ets.delivery.application.app.thirdservice.response.yunda.YundaInventoryQueryXmlVO;
import com.ets.delivery.application.app.thirdservice.response.yunda.YundaOrderCancelXmlVO;
import com.ets.delivery.application.app.thirdservice.response.yunda.YundaStockListVO;
import com.ets.delivery.application.common.bo.task.TaskLogisticsConfirmYundaBO;
import com.ets.delivery.application.common.bo.task.TaskLogisticsShipFailBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.config.WeChatRobotConfig;
import com.ets.delivery.application.common.config.yunda.YundaConfig;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOperateTypeEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaDeliveryOrderOrderTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaInventoryTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaLogiticsCodeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaProcessExtendPropsCodeEnum;
import com.ets.delivery.application.common.dto.logistics.LogisticsCancelOrderDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderConfirmDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderProcessDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.InventoryQueryVO;
import com.ets.delivery.application.common.vo.WarehouseStockListVO;
import com.ets.delivery.application.common.vo.alarm.LogisticsAlarmExcelVO;
import com.ets.delivery.application.common.vo.alarm.StockAlarmExcelVO;
import com.ets.delivery.application.common.vo.alarm.StorageStockAlarmFileVO;
import com.ets.delivery.application.common.vo.alarm.YundaDailyLogisticsAlarmExcelVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.LogisticsLogService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.application.infra.service.TaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class YundaStorageManage extends StorageBase {

    @Autowired
    private YundaConfig yundaConfig;

    @Autowired
    private DeliveryConfig deliveryConfig;

    @Autowired
    private WeChatRobotConfig weChatRobotConfig;

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private YundaBusiness yundaBusiness;

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    LogisticsService logisticsService;

    @Autowired
    LogisticsLogService logisticsLogService;

    @Autowired
    private WorkWeChatBusiness workWeChatBusiness;

    @Override
    public String addOrder(ExWarehouse exWarehouse, List<ExWarehouseDetail> detailList) {

        if (StringUtils.isEmpty(exWarehouse.getLogisticsCode()) ||
                !YundaLogiticsCodeEnum.list.contains(exWarehouse.getLogisticsCode())) {
            ToolsHelper.throwException("物流公司编码不正确");
        }
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 组装请求参数
        YundaDeliveryOrderCreateXmlDTO orderCreateDTO = new YundaDeliveryOrderCreateXmlDTO();

        // 发货单信息
        YundaDeliveryOrderCreateXmlDTO.DeliveryOrder deliveryOrder = new YundaDeliveryOrderCreateXmlDTO.DeliveryOrder();
        deliveryOrder.setDeliveryOrderCode(exWarehouse.getIsvUuid());
        deliveryOrder.setOrderType(YundaDeliveryOrderOrderTypeEnum.JYCK.getValue());
        deliveryOrder.setWarehouseCode(yundaConfig.getWarehouseCode());
        deliveryOrder.setCreateTime(timestamp);
        deliveryOrder.setPlaceOrderTime(timestamp);
        deliveryOrder.setOperateTime(timestamp);
        deliveryOrder.setShopNick(yundaConfig.getShopNick());
        deliveryOrder.setLogisticsCode(exWarehouse.getLogisticsCode());
        deliveryOrder.setRemark(exWarehouse.getRemark());

        // 联系人信息
        YundaDeliveryOrderCreateXmlDTO.DeliveryOrder.ReceiverInfo receiverInfo = new YundaDeliveryOrderCreateXmlDTO.DeliveryOrder.ReceiverInfo();
        receiverInfo.setName(exWarehouse.getConsigneeName());
        receiverInfo.setMobile(exWarehouse.getConsigneeMobile());
        receiverInfo.setProvince(exWarehouse.getConsigneeProvince());
        receiverInfo.setCity(exWarehouse.getConsigneeCity());
        receiverInfo.setArea(exWarehouse.getConsigneeArea());
        receiverInfo.setDetailAddress(exWarehouse.getConsigneeAddress());
        deliveryOrder.setReceiverInfo(receiverInfo);

        orderCreateDTO.setDeliveryOrder(deliveryOrder);

        // 设置多商品
        List<YundaDeliveryOrderCreateXmlDTO.OrderLine> orderLineList = new ArrayList<>();
        detailList.forEach(goods -> {
            YundaDeliveryOrderCreateXmlDTO.OrderLine orderLine = new YundaDeliveryOrderCreateXmlDTO.OrderLine();
            orderLine.setOwnerCode(yundaConfig.getCustomerId());
            orderLine.setItemCode(goods.getGoodsNo());
            orderLine.setPlanQty(goods.getQuantity());
            orderLineList.add(orderLine);
        });
        orderCreateDTO.setOrderLine(orderLineList);

        YundaDeliveryOrderCreateXmlVO createVO = yundaBusiness.deliveryOrderCreate(orderCreateDTO);
        if (ObjectUtil.isNotNull(createVO) && createVO.getFlag().equals("failure")) {
            ToolsHelper.throwException("韵达发货失败：" + createVO.getMessage(), createVO.getCode());
        }

        return exWarehouse.getIsvUuid();
    }

    @Override
    public void cancelOrder(LogisticsCancelOrderDTO cancelOrderDTO) {
        YundaOrderCancelXmlDTO orderCancelDTO = new YundaOrderCancelXmlDTO();
        orderCancelDTO.setOwnerCode(yundaConfig.getCustomerId());
        orderCancelDTO.setOrderCode(cancelOrderDTO.getLogisticsSn());
        orderCancelDTO.setOrderType(YundaDeliveryOrderOrderTypeEnum.JYCK.getValue());

        YundaOrderCancelXmlVO cancelVO = yundaBusiness.orderCancel(orderCancelDTO);
        if (ObjectUtil.isNotNull(cancelVO) && cancelVO.getFlag().equals("failure")) {
            ToolsHelper.throwException("韵达取消发货失败：" + cancelVO.getMessage(), cancelVO.getCode());
        }
    }

    @Override
    public void orderConfirm(LogisticsOrderConfirmDTO confirmDTO) {
        DeliveryConfirmDTO confirmData = (DeliveryConfirmDTO) confirmDTO.getOrderConfirmData();

        // 按SKU收集序列号和库存类型
        Map<String, TaskLogisticsConfirmYundaBO.SkuSerialInfo> skuSerialInfoMap = new HashMap<>();
        if (confirmData.getOrderLine() != null && !confirmData.getOrderLine().isEmpty()) {
            for (DeliveryConfirmDTO.OrderLine orderLine : confirmData.getOrderLine()) {
                String itemCode = orderLine.getItemCode();
                
                // 收集序列号和库存类型
                if (orderLine.getSn() != null && !orderLine.getSn().isEmpty()) {
                    String inventoryType = StringUtils.isNotEmpty(orderLine.getInventoryType()) ? orderLine.getInventoryType() : "ZP";
                    TaskLogisticsConfirmYundaBO.SkuSerialInfo skuSerialInfo = new TaskLogisticsConfirmYundaBO.SkuSerialInfo(orderLine.getSn(), inventoryType);
                    skuSerialInfoMap.put(itemCode, skuSerialInfo);
                }
            }
        }

        // 新增韵达发货确认task_record
        TaskLogisticsConfirmYundaBO confirmYundaBO = new TaskLogisticsConfirmYundaBO();
        confirmYundaBO.setLogisticsSn(confirmData.getDeliveryOrder().getDeliveryOrderCode());
        confirmYundaBO.setWarehouseNo(confirmData.getDeliveryOrder().getWarehouseCode());
        confirmYundaBO.setExpressCorpNo(confirmData.getAPackage().get(0).getLogisticsCode());
        confirmYundaBO.setExpressCorp(
                YundaLogiticsCodeEnum.map.getOrDefault(
                        confirmData.getAPackage().get(0).getLogisticsCode(),
                        confirmData.getAPackage().get(0).getLogisticsCode()
                )
        );
        confirmYundaBO.setExpressNumber(confirmData.getAPackage().get(0).getExpressCode());
        confirmYundaBO.setDeliveryStatus(confirmData.getDeliveryOrder().getStatus());
        confirmYundaBO.setDeliveryTime(confirmData.getDeliveryOrder().getOrderConfirmTime());
        confirmYundaBO.setSkuSerialInfoMap(skuSerialInfoMap);

        String content = JSON.toJSONString(confirmYundaBO);

        // 查询task_record
        TaskRecord taskRecord = taskRecordService.getOneByCondition(confirmData.getDeliveryOrder().getDeliveryOrderCode(),
                TaskRecordReferTypeEnum.TASK_LOGISTICS_CONFIRM_YUNDA.getType(),
                content
        );

        // 不存在则插入
        if (ObjectUtil.isNull(taskRecord)) {
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_CONFIRM_YUNDA.getType());
            taskRecordDTO.setReferSn(confirmData.getDeliveryOrder().getDeliveryOrderCode());
            taskRecordDTO.setNotifyContent(content);

            TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_CONFIRM_YUNDA).addAndPush(taskRecordDTO);
        }
    }

    @Override
    public void erpOrderConfirm(LogisticsOrderConfirmDTO confirmDTO) {
        DeliveryConfirmDTO confirmData = (DeliveryConfirmDTO) confirmDTO.getOrderConfirmData();

        String content = JSON.toJSONString(confirmDTO);

        // 查询task_record
        TaskRecord taskRecord = taskRecordService.getOneByCondition(confirmData.getDeliveryOrder().getDeliveryOrderCode(),
                TaskRecordReferTypeEnum.TASK_ERP_ORDER_CONFIRM.getType(),
                content
        );

        // 不存在则插入
        if (ObjectUtil.isNull(taskRecord)) {
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_ERP_ORDER_CONFIRM.getType());
            taskRecordDTO.setReferSn(confirmData.getDeliveryOrder().getDeliveryOrderCode());
            taskRecordDTO.setNotifyContent(content);

            TaskFactory.create(TaskRecordReferTypeEnum.TASK_ERP_ORDER_CONFIRM).addAndPush(taskRecordDTO);
        }
    }

    @Override
    public List<InventoryQueryVO> inventoryQuery(List<String> goodsCodeList) {
        // 组装参数
        YundaInventoryQueryXmlDTO inventoryQueryDTO = new YundaInventoryQueryXmlDTO();
        List<YundaInventoryQueryXmlDTO.criteria> criteriaList = new ArrayList<>();
        goodsCodeList.forEach(goodsCode -> {
            YundaInventoryQueryXmlDTO.criteria criteria = new YundaInventoryQueryXmlDTO.criteria();
            criteria.setWarehouseCode(yundaConfig.getWarehouseCode());
            criteria.setOwnerCode(yundaConfig.getCustomerId());
            criteria.setItemCode(goodsCode);
            criteria.setInventoryType(YundaInventoryTypeEnum.ZP.getValue());
            criteriaList.add(criteria);
        });
        inventoryQueryDTO.setCriteria(criteriaList);

        // 请求接口
        YundaInventoryQueryXmlVO inventoryQueryVO = yundaBusiness.inventoryQuery(inventoryQueryDTO);
        if (ObjectUtil.isNotNull(inventoryQueryVO) && inventoryQueryVO.getFlag().equals("failure")) {
            ToolsHelper.throwException("韵达库存查询失败：" + inventoryQueryVO.getMessage(), inventoryQueryVO.getCode());
        }

        // 处理结果
        if (ObjectUtil.isEmpty(inventoryQueryVO.getItem())) {
            return null;
        }
        List<InventoryQueryVO> inventoryQueryVOList = new ArrayList<>();
        inventoryQueryVO.getItem().forEach(goods -> {
            InventoryQueryVO queryVO = new InventoryQueryVO();
            queryVO.setWarehouseNo(goods.getWarehouseCode());
            queryVO.setGoodsCode(goods.getItemCode());
            queryVO.setStockNum(goods.getQuantity());
            inventoryQueryVOList.add(queryVO);
        });

        return inventoryQueryVOList;
    }

    @Override
    public void orderProcess(LogisticsOrderProcessDTO orderProcessDTO) {
        YundaOrderProcessDTO processData = ConverterRegistry.getInstance().convert(YundaOrderProcessDTO.class, orderProcessDTO.getOrderProcessData());

        // 异常需要记录
        Integer code = processData.getProcess().getExtendProps().getCode();
        if (ObjectUtil.isNotNull(code) && !code.equals(YundaProcessExtendPropsCodeEnum.NORMAL.getValue())) {
            String logisticsSn = processData.getOrder().getOrderCode();
            Logistics logistics = logisticsService.getByLogisticsSn(logisticsSn);
            if (ObjectUtil.isNotEmpty(logistics)) {
                // 发货单已取消
                if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue())) {
                    ToolsHelper.throwException(logisticsSn + "发货单已取消");
                }

                // 发货单已暂停
                if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_STOP.getValue())) {
                    ToolsHelper.throwException(logisticsSn + "发货单已暂停");
                }

                String remark = processData.getProcess().getRemark();
                String operateTime = processData.getProcess().getOperateTime();
                // 更新发货单
                logistics.setDeliveryRemark(remark);
                logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_STOP.getValue());
                logistics.setStatus(LogisticsStatusEnum.STATUS_STOP.getValue());
                logistics.setUpdatedAt(LocalDateTime.now());
                logisticsService.updateById(logistics);

                // 记录日志
                logisticsLogService.addLog(
                        logistics.getId(),
                        LogisticsOperateTypeEnum.TYPE_MODIFY.getCode(),
                        "韵达通知发货异常，错误码：" + code + " 错误信息：" + remark + " 时间：" + operateTime,
                        "system"
                );

                // 地址不送达
                if (code.equals(YundaProcessExtendPropsCodeEnum.ADDRESS_NOT_REACHABLE.getValue())) {
                    // 推送企业微信预警（不受业务类型限制，所有情况都通知）
                    try {
                        String markdown = "【发货暂停】地址不送达预警\n" +
                                "><font color=\"warning\">订单号</font>：" + logistics.getOrderSn() + "\n" +
                                "><font color=\"warning\">物流单号</font>：" + logisticsSn + "\n" +
                                "><font color=\"warning\">错误信息</font>：" + remark + "\n" +
                                "><font color=\"warning\">通知时间</font>：" + operateTime + "\n";

                        workWeChatBusiness.sendMarkdown(markdown, weChatRobotConfig.getLogisticsShipFailAlarmKey());
                    } catch (Exception e) {
                        log.error("【发货暂停】发送企业微信预警失败：{}", e.getMessage());
                    }

                    // 限制通知订单类型（只影响业务通知，不影响企业微信通知）
                    if (!deliveryConfig.getNotifyShipFailOrderTypeList().contains(logistics.getOrderType())) {
                        return;
                    }

                    // 通知内容
                    TaskLogisticsShipFailBO shipFailBO = new TaskLogisticsShipFailBO();
                    shipFailBO.setLogisticsSn(logisticsSn);
                    shipFailBO.setOrderSn(logistics.getOrderSn());
                    shipFailBO.setRemark(remark);
                    shipFailBO.setNotifyTime(operateTime);
                    if (StringUtils.isNotEmpty(logistics.getNotifyBackUrl())) {
                        shipFailBO.setNotifyUrl(logistics.getNotifyBackUrl());
                    } else {
                        shipFailBO.setNotifyUrl(deliveryConfig.getDefaultLogisticsNotifyUrl());
                    }
                    String content = JSON.toJSONString(shipFailBO);

                    // 创建task任务
                    TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                    taskRecordDTO.setReferSn(logisticsSn);
                    taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP_FAIL.getType());
                    taskRecordDTO.setNotifyContent(content);

                    TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP_FAIL).addAndPush(taskRecordDTO);
                }
            }
        }
    }

    @Override
    public List<WarehouseStockListVO> stockList(String startDate, String endDate) {
        // 请求参数
        YundaStockListDTO stockListDTO = new YundaStockListDTO();
        stockListDTO.setOwner(yundaConfig.getCustomerId());
        stockListDTO.setWarehouseCode(yundaConfig.getWarehouseCode());
        stockListDTO.setChannel(yundaConfig.getChannel().toString());
        stockListDTO.setStartDate(startDate);
        stockListDTO.setEndDate(endDate);

        // 请求接口
        YundaStockListVO stockListVO = yundaBusiness.stockList(stockListDTO);
        if (ObjectUtil.isNotNull(stockListVO) && !stockListVO.getSuccess()) {
            ToolsHelper.throwException("获取韵达库存失败：" + stockListVO.getMsg());
        }

        // 处理结果
        if (ObjectUtil.isEmpty(stockListVO.getData())) {
            return null;
        }
        List<WarehouseStockListVO> resultList = new ArrayList<>();
        stockListVO.getData().stream().filter(stockList-> stockList.getInvstat().equals("ZP")).forEach(stockList -> {
            WarehouseStockListVO stock = new WarehouseStockListVO();
            stock.setWarehouseNo(stockList.getWarehouseCode());
            stock.setSku(stockList.getSku());
            stock.setStock(stockList.getEndqty());
            stock.setDate(stockList.getSysdate());
            stock.setInStock(stockList.getInqty());
            stock.setOutStock(stockList.getOutqty());
            resultList.add(stock);
        });

        return resultList;
    }

    @Override
    public StorageStockAlarmFileVO stockAlarm(List<SupplyGoods> goodsList) {
        StorageStockAlarmFileVO storageStockAlarmFileVO = new StorageStockAlarmFileVO();
        List<StockAlarmExcelVO> stockAlarmList = new ArrayList<>();
        List<LogisticsAlarmExcelVO> logisticsAlarmList = new ArrayList<>();
        List<YundaDailyLogisticsAlarmExcelVO> yundaStockAlarmList = new ArrayList<>();

        String yesterday = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<WarehouseStockListVO> yesterdayStockList = this.stockList(yesterday, yesterday);
        if (ObjectUtil.isEmpty(yesterdayStockList)) {
            return storageStockAlarmFileVO;
        }
        Map<String, WarehouseStockListVO> stockMap = yesterdayStockList.stream().collect(Collectors.toMap(WarehouseStockListVO::getSku, stock -> stock));

        List<String> storageSkuList = goodsList.stream().map(SupplyGoods::getGoodsCode).collect(Collectors.toList());

        // 统计我司发货总量Map
        Map<String, Double> yesterdaySumMap = logisticsBusiness.getLogisticsSumMap(StorageCodeEnum.YUNDA.getValue(), storageSkuList, 1);
        Map<String, Double> threeDaySumMap = logisticsBusiness.getLogisticsSumMap(StorageCodeEnum.YUNDA.getValue(), storageSkuList, 3);
        Map<String, Double> sevenDaySumMap = logisticsBusiness.getLogisticsSumMap(StorageCodeEnum.YUNDA.getValue(), storageSkuList, 7);

        // 统计我司平均发货量Map
        Map<String, Double> yesterdayAvgMap = logisticsBusiness.getLogisticsAverageMap(StorageCodeEnum.YUNDA.getValue(), storageSkuList, 1, 0);
        Map<String, Double> threeDayAvgMap = logisticsBusiness.getLogisticsAverageMap(StorageCodeEnum.YUNDA.getValue(), storageSkuList, 3, 0);
        Map<String, Double> sevenDayAvgMap = logisticsBusiness.getLogisticsAverageMap(StorageCodeEnum.YUNDA.getValue(), storageSkuList, 7, 0);

        goodsList.forEach(goods -> {
            // 仓库库存
            WarehouseStockListVO stock = stockMap.getOrDefault(goods.getGoodsCode(), new WarehouseStockListVO());
            // 统计昨天总发货量
            Double yesterdaySum = yesterdaySumMap.getOrDefault(goods.getGoodsCode(), 0.0);
            // 统计近3天总发货量
            Double threeDaySum = threeDaySumMap.getOrDefault(goods.getGoodsCode(), 0.0);
            // 统计近7天总发货量
            Double sevenDaySum = sevenDaySumMap.getOrDefault(goods.getGoodsCode(), 0.0);

            // 统计昨日发货量
            Double yesterdayAvg = yesterdayAvgMap.getOrDefault(goods.getGoodsCode(), 0.0);
            // 统计近3天平均发货量
            Double threeDayAvg = threeDayAvgMap.getOrDefault(goods.getGoodsCode(), 0.0);
            // 统计近7天平均发货量
            Double sevenDayAvg = sevenDayAvgMap.getOrDefault(goods.getGoodsCode(), 0.0);

            // 预计可发天数(库存数/过去7天发货平均数)
            int predictDay = 0;
            if (sevenDayAvg > 0) {
                predictDay = (int) (stock.getStock() / sevenDayAvg);
            }
            // 有库存 且 7天发货总量0 天数999999
            if (stock.getStock() > 0 && sevenDaySum == 0) {
                predictDay = 999999;
            }

            // 涨跌幅度(（昨天发货量-近7天平均发货量）/近7天平均发货量*100%)
            BigDecimal floatPercent = new BigDecimal(0);
            if (sevenDayAvg.intValue() != 0) {
                // 去掉小数点计算涨幅
                yesterdayAvg = Double.parseDouble(String.format("%.0f", yesterdayAvg));
                sevenDayAvg = Double.parseDouble(String.format("%.0f", sevenDayAvg));

                double percent = (yesterdayAvg - sevenDayAvg) / sevenDayAvg;
                floatPercent = (new BigDecimal(percent * 100)).setScale(2, RoundingMode.HALF_UP);
            }
            if (threeDayAvg.intValue() != 0) {
                threeDayAvg = Double.parseDouble(String.format("%.0f", threeDayAvg));
            }

            StockAlarmExcelVO stockAlarmExcelVO = StockAlarmExcelVO.builder()
                    .goodsUnionCode(goods.getGoodsUnionCode())
                    .goodsSku(goods.getGoodsCode())
                    .goodsName(goods.getGoodsName())
                    .stock(stock.getStock())
                    .yesterdayLogisticsNum(yesterdaySum.intValue())
                    .threeDayLogisticsNum(threeDaySum.intValue())
                    .sevenDayLogisticsNum(sevenDaySum.intValue())
                    .predictDayNum(predictDay)
                    .build();
            stockAlarmList.add(stockAlarmExcelVO);

            LogisticsAlarmExcelVO logisticsAlarmExcelVO = LogisticsAlarmExcelVO.builder()
                    .goodsUnionCode(goods.getGoodsUnionCode())
                    .goodsSku(goods.getGoodsCode())
                    .goodsName(goods.getGoodsName())
                    .yesterdayLogisticsNum(yesterdayAvg.intValue())
                    .threeDayAvgLogisticsNum(threeDayAvg.intValue())
                    .sevenDayAvgLogisticsNum(sevenDayAvg.intValue())
                    .floatPercent(floatPercent)
                    .build();
            logisticsAlarmList.add(logisticsAlarmExcelVO);

            // 出库盈亏数
            Integer profitAndLoss = stock.getOutStock() - yesterdaySum.intValue();

            YundaDailyLogisticsAlarmExcelVO yundaDailyLogisticsAlarmExcelVO = YundaDailyLogisticsAlarmExcelVO.builder()
                    .goodsUnionCode(goods.getGoodsUnionCode())
                    .goodsSku(goods.getGoodsCode())
                    .goodsName(goods.getGoodsName())
                    .yesterdayLogisticsNum(yesterdaySum.intValue())
                    .yundaYesterdayOutStockNum(stock.getOutStock())
                    .profitAndLoss(profitAndLoss)
                    .build();
            yundaStockAlarmList.add(yundaDailyLogisticsAlarmExcelVO);
        });

        storageStockAlarmFileVO.setStockAlarmList(stockAlarmList);
        storageStockAlarmFileVO.setLogisticsAlarmList(logisticsAlarmList);
        storageStockAlarmFileVO.setYundaStockAlarmList(yundaStockAlarmList);
        return storageStockAlarmFileVO;
    }
}
