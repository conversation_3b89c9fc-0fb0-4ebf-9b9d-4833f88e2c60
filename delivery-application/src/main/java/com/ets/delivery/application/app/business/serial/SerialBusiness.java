package com.ets.delivery.application.app.business.serial;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaInventoryTypeEnum;
import com.ets.delivery.application.common.dto.serial.AdminSerialListDTO;
import com.ets.delivery.application.common.dto.serial.AdminSerialLogListDTO;
import com.ets.delivery.application.common.dto.serial.AdminStockSerialListDTO;
import com.ets.delivery.application.common.vo.serial.AdminSerialListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialLogListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialSelectOptionsVO;
import com.ets.delivery.application.common.vo.serial.AdminStockSerialListVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SerialBusiness {

    @Autowired
    private SerialService serialService;

    @Autowired
    private SerialLogService serialLogService;

    @Autowired
    private StockGoodsInfoService stockGoodsInfoService;

    @Autowired
    private StockGoodsSerialService stockGoodsSerialService;

    @Autowired
    private LogisticsSkuSerialService logisticsSkuSerialService;

    @Autowired
    private StorageRecordSerialService storageRecordSerialService;

    @Autowired
    private LogisticsSkuService logisticsSkuService;

    private static final Pattern SERIAL_PATTERN = Pattern.compile("^[A-Za-z0-9]+$");

    /**
     * 序列号列表查询
     */
    public IPage<AdminSerialListVO> getList(AdminSerialListDTO dto) {
        IPage<Serial> page = serialService.getPage(dto);
        return page.convert(serial -> {
            AdminSerialListVO vo = new AdminSerialListVO();
            BeanUtil.copyProperties(serial, vo);
            return vo;
        });
    }

    /**
     * 序列号日志列表查询
     */
    public IPage<AdminSerialLogListVO> getLogList(AdminSerialLogListDTO dto) {
        IPage<SerialLog> page = serialLogService.getPage(dto);
        return page.convert(serialLog -> {
            AdminSerialLogListVO vo = new AdminSerialLogListVO();
            BeanUtil.copyProperties(serialLog, vo);
            return vo;
        });
    }

    /**
     * 库存序列号列表查询
     */
    public IPage<AdminStockSerialListVO> getStockSerialList(AdminStockSerialListDTO dto) {
        IPage<StockGoodsSerial> page = stockGoodsSerialService.getPage(dto);
        return page.convert(serial -> {
            AdminStockSerialListVO vo = new AdminStockSerialListVO();
            BeanUtil.copyProperties(serial, vo);
            return vo;
        });
    }

    /**
     * 获取选择项
     */
    public AdminSerialSelectOptionsVO getSelectOptions() {
        AdminSerialSelectOptionsVO vo = new AdminSerialSelectOptionsVO();
        vo.setStatusList(SerialStatusEnum.getSelectOptions());
        vo.setInventoryTypeList(YundaInventoryTypeEnum.getSelectOptions());
        vo.setStorageList(StorageCodeEnum.getSelectOptions());
        return vo;
    }

    /**
     * 处理寄回流程中的序列号（带仓库编码）
     */
    public void handleReturnSerials(SerialProcessBO processBO) {
        if (ObjectUtils.isEmpty(processBO.getSerialList())) {
            return;
        }

        processBO.setTargetStatus(SerialStatusEnum.RETURNED);
        validateSerialNumbers(processBO.getSerialList());

        SerialProcessResult result = processSerials(processBO.getSerialList(), (serialNo, existSerial) -> {
            if (existSerial != null) {
                // 更新现有序列号状态为寄回
                existSerial.setStatus(processBO.getTargetStatus().getValue());
                existSerial.setStorageCode(processBO.getStorageCode());
                // 更新业务来源和业务单号
                existSerial.setLatestBusinessSource(processBO.getBusinessSource());
                existSerial.setLatestBusinessSn(processBO.getBusinessSn());
                return existSerial;
            } else {
                // 序列号不存在，创建新记录，状态设为寄回
                Serial serial = new Serial();
                serial.setSerialNo(serialNo);
                serial.setStatus(processBO.getTargetStatus().getValue());
                serial.setStorageCode(processBO.getStorageCode());
                // 设置业务来源和业务单号
                serial.setLatestBusinessSource(processBO.getBusinessSource());
                serial.setLatestBusinessSn(processBO.getBusinessSn());
                return serial;
            }
        });

        // 批量保存和更新
        batchSaveAndUpdate(result);

        // 寄回操作需要获取现有序列号的库存类型
        if (SerialStatusEnum.RETURNED.equals(processBO.getTargetStatus())) {
            List<Serial> existingSerials = serialService.getBySerialNos(processBO.getSerialList());
            Map<String, Serial> existingSerialMap = existingSerials.stream()
                    .collect(Collectors.toMap(Serial::getSerialNo, Function.identity()));
            
            // 为每个序列号设置正确的库存类型
            processBO.getSerialList().forEach(serialNo -> {
                Serial existSerial = existingSerialMap.get(serialNo);
                if (existSerial != null && StringUtils.isEmpty(processBO.getInventoryType())) {
                    processBO.setInventoryType(existSerial.getInventoryType());
                }
            });
            
            // 设置寄回专用备注
            if (StringUtils.isEmpty(processBO.getRemark())) {
                processBO.setRemark("寄回流程更新");
            }
        }
        
        // 批量创建日志记录
        batchCreateLogs(processBO);

        // 更新StorageRecordSerial表数据
        updateStorageRecordSerial(processBO.getSerialList(), processBO.getBusinessSn());
    }

    /**
     * 处理入库流程中的序列号（支持库存类型）
     */
    public void handleStockInSerials(SerialProcessBO processBO) {
        processBO.setTargetStatus(SerialStatusEnum.IN_STOCK);
        handleStockSerials(processBO);
    }

    /**
     * 处理出库流程中的序列号（支持库存类型）
     */
    public void handleStockOutSerials(SerialProcessBO processBO) {
        processBO.setTargetStatus(SerialStatusEnum.OUT_STOCK);
        handleStockSerials(processBO);
    }

    /**
     * 通用库存处理方法（合并出入库逻辑）
     */
    private void handleStockSerials(SerialProcessBO processBO) {
        if (ObjectUtils.isEmpty(processBO.getSerialList())) {
            return;
        }

        validateSerialNumbers(processBO.getSerialList());

        SerialProcessResult result = processSerials(processBO.getSerialList(), (serialNo, existSerial) -> {
            if (existSerial != null) {
                // 更新现有序列号状态和库存类型
                existSerial.setStatus(processBO.getTargetStatus().getValue());
                if (StringUtils.isNotEmpty(processBO.getInventoryType())) {
                    existSerial.setInventoryType(processBO.getInventoryType());
                }
                // 更新业务来源和业务单号
                existSerial.setLatestBusinessSource(processBO.getBusinessSource());
                existSerial.setLatestBusinessSn(processBO.getBusinessSn());
                return existSerial;
            } else {
                // 序列号不存在，创建新记录
                Serial newSerial = new Serial();
                newSerial.setSerialNo(serialNo);
                newSerial.setStatus(processBO.getTargetStatus().getValue());
                if (StringUtils.isNotEmpty(processBO.getInventoryType())) {
                    newSerial.setInventoryType(processBO.getInventoryType());
                }
                // 设置业务来源和业务单号
                newSerial.setLatestBusinessSource(processBO.getBusinessSource());
                newSerial.setLatestBusinessSn(processBO.getBusinessSn());
                newSerial.setStorageCode(processBO.getStorageCode());
                newSerial.setStorageSku(processBO.getStorageSku());
                return newSerial;
            }
        });

        // 批量保存和更新
        batchSaveAndUpdate(result);
        
        // 出库、发货、寄回时处理不存在序列号的预警
        if ((SerialStatusEnum.OUT_STOCK.equals(processBO.getTargetStatus()) || 
             SerialStatusEnum.RETURNED.equals(processBO.getTargetStatus())) && 
            !result.getNewSerials().isEmpty()) {
            handleMissingSerialWarning(result.getNewSerials(), processBO.getBusinessSn(), processBO.getStorageSku());
        }

        // 批量创建日志记录
        batchCreateLogs(processBO);

        // 更新StockGoodsSerial表数据
        updateStockGoodsSerial(processBO.getSerialList(), processBO.getBusinessSn(), processBO.getStorageSku());
    }

    /**
     * 处理发货流程中的序列号（支持库存类型）
     */
    public void handleDeliverySerials(SerialProcessBO processBO) {
        if (ObjectUtils.isEmpty(processBO.getSerialList())) {
            return;
        }

        processBO.setTargetStatus(SerialStatusEnum.OUT_STOCK);
        validateSerialNumbers(processBO.getSerialList());

        SerialProcessResult result = processSerials(processBO.getSerialList(), (serialNo, existSerial) -> {
                if (existSerial != null) {
                    // 更新现有序列号的库存类型，状态保持出库
                    if (StringUtils.isNotEmpty(processBO.getInventoryType())) {
                        existSerial.setInventoryType(processBO.getInventoryType());
                    }
                    // 更新业务来源和业务单号
                    existSerial.setLatestBusinessSource(processBO.getBusinessSource());
                    existSerial.setLatestBusinessSn(processBO.getBusinessSn());
                    return existSerial;
                } else {
                    // 序列号不存在，创建新记录，状态设为出库
                    Serial serial = new Serial();
                    serial.setSerialNo(serialNo);
                    if (StringUtils.isNotEmpty(processBO.getInventoryType())) {
                        serial.setInventoryType(processBO.getInventoryType());
                    }
                    serial.setStatus(SerialStatusEnum.OUT_STOCK.getValue());
                    serial.setLatestBusinessSource(processBO.getBusinessSource());
                    serial.setLatestBusinessSn(processBO.getBusinessSn());
                    serial.setStorageCode(processBO.getStorageCode());
                    serial.setStorageSku(processBO.getStorageSku());
                    return serial;
                }
            });

        // 批量保存和更新
        batchSaveAndUpdate(result);

        // 发货时处理不存在序列号的预警
        if (!result.getNewSerials().isEmpty()) {
            handleMissingSerialWarning(result.getNewSerials(), processBO.getBusinessSn(), processBO.getStorageSku());
        }

        // 批量创建日志记录
        batchCreateLogs(processBO);

        // 更新LogisticsSkuSerial表数据
        updateLogisticsSkuSerial(processBO.getSerialList(), processBO.getBusinessSn(), processBO.getStorageSku());
    }

    /**
     * 序列号处理结果
     */
    @Data
    private static class SerialProcessResult {
        private final List<Serial> newSerials = new ArrayList<>();
        private final List<Serial> updateSerials = new ArrayList<>();
        
        public void addNew(Serial serial) {
            LocalDateTime now = LocalDateTime.now();
            serial.setCreatedAt(now);
            serial.setUpdatedAt(now);
            newSerials.add(serial);
        }
        
        public void addUpdate(Serial serial) {
            serial.setUpdatedAt(LocalDateTime.now());
            updateSerials.add(serial);
        }
    }

    /**
     * 序列号处理器接口
     */
    @FunctionalInterface
    private interface SerialProcessor {
        Serial process(String serialNo, Serial existSerial);
    }

    /**
     * 通用序列号处理方法
     */
    private SerialProcessResult processSerials(List<String> serialList, SerialProcessor processor) {
        SerialProcessResult result = new SerialProcessResult();
        
        // 批量查询现有序列号
        List<Serial> existingSerials = serialService.getBySerialNos(serialList);
        Map<String, Serial> existingSerialMap = existingSerials.stream()
                .collect(Collectors.toMap(Serial::getSerialNo, Function.identity()));

        // 处理每个序列号
        for (String serialNo : serialList) {
            Serial existSerial = existingSerialMap.get(serialNo);
            Serial processedSerial = processor.process(serialNo, existSerial);
            
            if (existSerial != null) {
                result.addUpdate(processedSerial);
            } else {
                result.addNew(processedSerial);
            }
        }
        
        return result;
    }

    /**
     * 批量保存和更新序列号
     */
    private void batchSaveAndUpdate(SerialProcessResult result) {
        if (!result.getNewSerials().isEmpty()) {
            serialService.saveBatch(result.getNewSerials());
        }
        
        if (!result.getUpdateSerials().isEmpty()) {
            serialService.updateBatchById(result.getUpdateSerials());
        }
    }

    /**
     * 批量创建日志记录
     */
    private void batchCreateLogs(SerialProcessBO processBO) {
        List<SerialLog> serialLogs = processBO.getSerialList().stream()
                .map(serialNo -> serialLogService.buildLog(serialNo, processBO.getTargetStatus().getValue(), 
                        processBO.getBusinessSource(), processBO.getBusinessSn(), processBO.getOperator(), processBO.getRemark(), processBO.getInventoryType()))
                .collect(Collectors.toList());
        
        if (!serialLogs.isEmpty()) {
            serialLogService.saveBatch(serialLogs);
        }
    }



    /**
     * 处理不存在序列号的预警
     */
    private void handleMissingSerialWarning(List<Serial> newSerials, String businessSn, String storageSku) {
        List<String> notFoundSerialNos = newSerials.stream()
                .map(Serial::getSerialNo)
                .collect(Collectors.toList());
        
        log.warn("出库流程发现{}个不存在的序列号，已新增记录: 出库单={}, 序列号列表={}", 
                newSerials.size(), businessSn, notFoundSerialNos);
    }

    /**
     * 验证序列号格式
     */
    private void validateSerialNumbers(List<String> serialList) {
        for (String serialNo : serialList) {
            if (StringUtils.isEmpty(serialNo) || !SERIAL_PATTERN.matcher(serialNo).matches()) {
                throw new IllegalArgumentException("序列号格式不正确: " + serialNo);
            }
        }
    }

    /**
     * 更新StockGoodsSerial表数据
     */
    private void updateStockGoodsSerial(List<String> serialList, String businessSn, String storageSku) {
        try {
            // 查询商品信息
            StockGoodsInfo goodsInfo = stockGoodsInfoService.getByGoodsCode(businessSn, storageSku);
            if (goodsInfo == null) {
                log.warn("未找到商品信息: storageSku={}", storageSku);
                return;
            }

            // 批量创建StockGoodsSerial记录
            List<StockGoodsSerial> stockGoodsSerials = serialList.stream()
                    .map(serialNo -> {
                        StockGoodsSerial stockGoodsSerial = new StockGoodsSerial();
                        stockGoodsSerial.setSerialNo(serialNo);
                        stockGoodsSerial.setStockSn(businessSn);
                        stockGoodsSerial.setStockGoodsId(goodsInfo.getId());
                        stockGoodsSerial.setCreatedAt(LocalDateTime.now());
                        stockGoodsSerial.setUpdatedAt(LocalDateTime.now());
                        return stockGoodsSerial;
                    })
                    .collect(Collectors.toList());

            if (!stockGoodsSerials.isEmpty()) {
                stockGoodsSerialService.saveBatch(stockGoodsSerials);
            }
        } catch (Exception e) {
            log.error("更新StockGoodsSerial表数据失败: businessSn={}, storageSku={}", businessSn, storageSku, e);
        }
    }

    /**
     * 更新LogisticsSkuSerial表数据（发货处理）
     */
    private void updateLogisticsSkuSerial(List<String> serialList, String businessSn, String storageSku) {
        try {
            log.info("开始更新LogisticsSkuSerial表数据: businessSn={}, 序列号数量={}", businessSn, serialList.size());

            LogisticsSku logisticsSku = logisticsSkuService.getByStorageSku(businessSn, storageSku);
            if (logisticsSku == null) {
                log.warn("未找到发货记录商品：发货单={} sku={}", businessSn, storageSku);
                return;
            }

            // 批量创建LogisticsSkuSerial记录
            List<LogisticsSkuSerial> logisticsSkuSerials = serialList.stream()
                    .map(serialNo -> {
                        LogisticsSkuSerial logisticsSkuSerial = new LogisticsSkuSerial();
                        logisticsSkuSerial.setSerialNo(serialNo);
                        logisticsSkuSerial.setLogisticsSn(businessSn);
                        logisticsSkuSerial.setLogisticsSkuId(logisticsSku.getId());
                        logisticsSkuSerial.setCreatedAt(LocalDateTime.now());
                        logisticsSkuSerial.setUpdatedAt(LocalDateTime.now());
                        return logisticsSkuSerial;
                    })
                    .collect(Collectors.toList());

            if (!logisticsSkuSerials.isEmpty()) {
                logisticsSkuSerialService.saveBatch(logisticsSkuSerials);
                log.info("成功保存LogisticsSkuSerial记录: 数量={}", logisticsSkuSerials.size());
            }
            
        } catch (Exception e) {
            log.error("更新LogisticsSkuSerial表数据失败: businessSn={}", businessSn, e);
        }
    }

    /**
     * 更新StorageRecordSerial表数据（寄回处理）
     */
    private void updateStorageRecordSerial(List<String> serialList, String businessSn) {
        try {
            log.info("开始更新StorageRecordSerial表数据: businessSn={}, 序列号数量={}", businessSn, serialList.size());
            
            // 批量创建StorageRecordSerial记录
            List<StorageRecordSerial> storageRecordSerials = serialList.stream()
                    .map(serialNo -> {
                        StorageRecordSerial storageRecordSerial = new StorageRecordSerial();
                        storageRecordSerial.setSerialNo(serialNo);
                        storageRecordSerial.setRecordSn(businessSn);
                        storageRecordSerial.setCreatedAt(LocalDateTime.now());
                        storageRecordSerial.setUpdatedAt(LocalDateTime.now());
                        return storageRecordSerial;
                    })
                    .collect(Collectors.toList());

            if (!storageRecordSerials.isEmpty()) {
                storageRecordSerialService.saveBatch(storageRecordSerials);
                log.info("成功保存StorageRecordSerial记录: 数量={}", storageRecordSerials.size());
            }
            
        } catch (Exception e) {
            log.error("更新StorageRecordSerial表数据失败: businessSn={}", businessSn, e);
        }
    }
}