package com.ets.delivery.application.app.business.serial;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.delivery.application.app.factory.serial.SerialFactory;
import com.ets.delivery.application.app.factory.serial.impl.SerialBase;
import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.bo.serial.SerialChangeResultBO;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.common.consts.serial.SerialTypeEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaInventoryTypeEnum;
import com.ets.delivery.application.common.dto.serial.AdminSerialListDTO;
import com.ets.delivery.application.common.dto.serial.AdminSerialLogListDTO;
import com.ets.delivery.application.common.dto.serial.AdminStockSerialListDTO;
import com.ets.delivery.application.common.vo.serial.AdminSerialListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialLogListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialSelectOptionsVO;
import com.ets.delivery.application.common.vo.serial.AdminStockSerialListVO;
import com.ets.delivery.application.infra.entity.Serial;
import com.ets.delivery.application.infra.entity.SerialLog;
import com.ets.delivery.application.infra.entity.StockGoodsSerial;
import com.ets.delivery.application.infra.service.SerialLogService;
import com.ets.delivery.application.infra.service.SerialService;
import com.ets.delivery.application.infra.service.StockGoodsSerialService;
import com.ets.delivery.application.infra.service.StorageRecordSerialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SerialBusiness {

    @Autowired
    private SerialService serialService;

    @Autowired
    private SerialLogService serialLogService;

    @Autowired
    private StockGoodsSerialService stockGoodsSerialService;

    @Autowired
    private StorageRecordSerialService storageRecordSerialService;

    /**
     * 序列号列表查询
     */
    public IPage<AdminSerialListVO> getList(AdminSerialListDTO dto) {
        IPage<Serial> page = serialService.getPage(dto);
        return page.convert(serial -> {
            AdminSerialListVO vo = new AdminSerialListVO();
            BeanUtil.copyProperties(serial, vo);
            return vo;
        });
    }

    /**
     * 序列号日志列表查询
     */
    public IPage<AdminSerialLogListVO> getLogList(AdminSerialLogListDTO dto) {
        IPage<SerialLog> page = serialLogService.getPage(dto);
        return page.convert(serialLog -> {
            AdminSerialLogListVO vo = new AdminSerialLogListVO();
            BeanUtil.copyProperties(serialLog, vo);
            return vo;
        });
    }

    /**
     * 库存序列号列表查询
     */
    public IPage<AdminStockSerialListVO> getStockSerialList(AdminStockSerialListDTO dto) {
        IPage<StockGoodsSerial> page = stockGoodsSerialService.getPage(dto);
        return page.convert(serial -> {
            AdminStockSerialListVO vo = new AdminStockSerialListVO();
            BeanUtil.copyProperties(serial, vo);
            return vo;
        });
    }

    /**
     * 获取选择项
     */
    public AdminSerialSelectOptionsVO getSelectOptions() {
        AdminSerialSelectOptionsVO vo = new AdminSerialSelectOptionsVO();
        vo.setStatusList(SerialStatusEnum.getSelectOptions());
        vo.setInventoryTypeList(YundaInventoryTypeEnum.getSelectOptions());
        vo.setStorageList(StorageCodeEnum.getSelectOptions());
        return vo;
    }

    /**
     * 通用序列号处理方法
     * @param serialType 序列号处理类型
     * @param processBO 序列号处理业务对象
     */
    public void processSerials(SerialTypeEnum serialType, SerialProcessBO processBO) {
        SerialBase serial = SerialFactory.create(serialType);
        serial.process(processBO);
    }

    /**
     * 根据记录单号获取序列号列表
     *
     * @param recordSn 记录单号
     * @return 序列号列表
     */
    public List<String> getSerialListByRecordSn(String recordSn) {
        return storageRecordSerialService.getSerialListByRecordSn(recordSn);
    }



    /**
     * 检测序列号变更并处理去重
     *
     * @param recordSn 记录单号
     * @param newSerialList 新的序列号列表
     * @return 序列号变更检测结果
     */
    public SerialChangeResultBO detectSerialChange(String recordSn, List<String> newSerialList) {
        // 查询原有的序列号记录
        List<String> originalSerialList = new ArrayList<>();
        try {
            originalSerialList = getSerialListByRecordSn(recordSn);
        } catch (Exception e) {
            log.warn("获取原有序列号记录失败，记录单号：{}，错误：{}", recordSn, e.getMessage());
        }

        // 处理新序列号列表（去重）
        List<String> processedNewSerialList = newSerialList != null ? newSerialList : new ArrayList<>();
        if (!processedNewSerialList.isEmpty()) {
            processedNewSerialList = processedNewSerialList.stream()
                    .distinct()
                    .collect(Collectors.toList());
        }

        // 比较序列号是否有变动
        boolean serialChanged = !originalSerialList.equals(processedNewSerialList);

        return new SerialChangeResultBO(originalSerialList, processedNewSerialList, serialChanged);
    }

    /**
     * 处理存储记录序列号的更新
     * 包括：1. 处理被移除的序列号日志 2. 删除旧的序列号记录 3. 处理新的序列号状态更新
     *
     * @param recordSn 存储记录单号
     * @param originalSerialList 原有序列号列表
     * @param newSerialList 新序列号列表
     * @param operator 操作人
     * @param storageCode 仓库编码
     * @param businessSource 业务来源（如"寄回"）
     */
    public void processStorageRecordSerialUpdate(String recordSn, List<String> originalSerialList,
                                               List<String> newSerialList, String operator,
                                               String storageCode, String businessSource) {
        // 1. 处理被移除的序列号日志
        processRemovedSerials(originalSerialList, newSerialList, recordSn, operator,
            businessSource, "从" + businessSource + "记录中移除序列号");

        // 2. 删除旧的序列号记录
        try {
            storageRecordSerialService.deleteByRecordSn(recordSn);
            log.info("删除存储记录的序列号记录，记录单号：{}，原序列号：{}，新序列号：{}",
                recordSn, originalSerialList, newSerialList);
        } catch (Exception e) {
            log.error("删除原有序列号记录失败，记录单号：{}，错误：{}", recordSn, e.getMessage());
            throw e; // 重新抛出异常，让调用方决定如何处理
        }

        // 3. 处理新的序列号状态更新
        if (newSerialList != null && !newSerialList.isEmpty()) {
            SerialProcessBO processBO = SerialProcessBO.builder()
                .serialList(newSerialList)
                .businessSn(recordSn)
                .storageSku("")
                .inventoryType("")
                .operator(operator)
                .storageCode(storageCode)
                .businessSource(businessSource)
                .build();
            processSerials(SerialTypeEnum.RETURN, processBO);
        }
    }

    /**
     * 处理被移除的序列号
     * 为被移除的序列号创建日志记录，表示它们不再与指定业务记录关联
     *
     * @param originalSerialList 原有序列号列表
     * @param newSerialList 新序列号列表
     * @param businessSn 业务单号
     * @param operator 操作人
     * @param businessSource 业务来源（如"寄回"）
     * @param remark 备注信息
     */
    public void processRemovedSerials(List<String> originalSerialList, List<String> newSerialList,
                                    String businessSn, String operator, String businessSource, String remark) {
        if (originalSerialList == null || originalSerialList.isEmpty()) {
            return;
        }

        if (newSerialList == null) {
            newSerialList = List.of();
        }

        // 找出被移除的序列号（原有序列号中不在新序列号中的）
        List<String> finalNewSerialList = newSerialList;
        List<String> removedSerialList = originalSerialList.stream()
                .filter(serialNo -> !finalNewSerialList.contains(serialNo))
                .collect(Collectors.toList());

        // 为被移除的序列号创建日志记录
        if (!removedSerialList.isEmpty()) {
            try {
                // 批量创建移除日志记录
                List<SerialLog> removeLogs = removedSerialList.stream()
                        .map(serialNo -> serialLogService.buildLog(serialNo, SerialStatusEnum.IN_STOCK.getValue(),
                                businessSource, businessSn, operator, remark, ""))
                        .collect(Collectors.toList());

                if (!removeLogs.isEmpty()) {
                    serialLogService.saveBatch(removeLogs);
                }

                log.info("为被移除的序列号创建日志记录，业务单号：{}，移除序列号：{}", businessSn, removedSerialList);
            } catch (Exception e) {
                log.error("处理被移除序列号日志失败，业务单号：{}，移除序列号：{}，错误：{}",
                    businessSn, removedSerialList, e.getMessage());
                throw e; // 重新抛出异常，让调用方决定如何处理
            }
        }
    }

}