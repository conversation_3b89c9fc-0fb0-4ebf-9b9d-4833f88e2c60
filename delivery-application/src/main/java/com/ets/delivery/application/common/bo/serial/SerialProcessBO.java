package com.ets.delivery.application.common.bo.serial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;

import java.util.List;

/**
 * 序列号处理业务对象
 * 用于封装序列号处理方法的参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SerialProcessBO {

    /**
     * 序列号列表
     */
    private List<String> serialList;

    /**
     * 业务单号（发货单号、入库单号等）
     */
    private String businessSn;

    /**
     * 仓库SKU编码
     */
    private String storageSku;

    /**
     * 库存类型（ZP/CC/JS/XS等）
     */
    private String inventoryType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仓库编码
     */
    private String storageCode;

    /**
     * 业务来源
     */
    private String businessSource;

    /**
     * 目标状态
     */
    private SerialStatusEnum targetStatus;

    /**
     * 创建入库处理BO
     */
    public static SerialProcessBO createStockInBO(List<String> serialList, String businessSn, String storageSku, 
                                                  String inventoryType, String operator, String remark, 
                                                  String storageCode, String businessSource) {
        return SerialProcessBO.builder()
                .serialList(serialList)
                .businessSn(businessSn)
                .storageSku(storageSku)
                .inventoryType(inventoryType)
                .operator(operator)
                .remark(remark)
                .storageCode(storageCode)
                .businessSource(businessSource)
                .build();
    }

    /**
     * 创建出库处理BO
     */
    public static SerialProcessBO createStockOutBO(List<String> serialList, String businessSn, String storageSku, 
                                                   String inventoryType, String operator, String remark, 
                                                   String storageCode, String businessSource) {
        return SerialProcessBO.builder()
                .serialList(serialList)
                .businessSn(businessSn)
                .storageSku(storageSku)
                .inventoryType(inventoryType)
                .operator(operator)
                .remark(remark)
                .storageCode(storageCode)
                .businessSource(businessSource)
                .build();
    }

    /**
     * 创建发货处理BO
     */
    public static SerialProcessBO createDeliveryBO(List<String> serialList, String businessSn, String storageSku, 
                                                   String inventoryType, String operator, String remark, 
                                                   String storageCode, String businessSource) {
        return SerialProcessBO.builder()
                .serialList(serialList)
                .businessSn(businessSn)
                .storageSku(storageSku)
                .inventoryType(inventoryType)
                .operator(operator)
                .remark(remark)
                .storageCode(storageCode)
                .businessSource(businessSource)
                .build();
    }

    /**
     * 创建寄回处理BO
     */
    public static SerialProcessBO createReturnBO(List<String> serialList, String businessSn, String operator, 
                                                 String storageCode, String storageSku, String businessSource) {
        return SerialProcessBO.builder()
                .serialList(serialList)
                .businessSn(businessSn)
                .operator(operator)
                .storageCode(storageCode)
                .storageSku(storageSku)
                .businessSource(businessSource)
                .build();
    }
}